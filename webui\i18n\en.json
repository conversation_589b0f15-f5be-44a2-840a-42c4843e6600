{"Language": "English", "Translation": {"Video Script Configuration": "**Video Script Configuration**", "Video Script Generate": "Generate Video Script", "Video Subject": "Video Subject (Given a keyword, :red[AI auto-generates] video script)", "Script Language": "Language of the generated video script (Usually, AI automatically outputs according to the language of the input subject)", "Script Files": "Script Files", "Generate Video Script and Keywords": "Click to use AI to generate **Video Script** and **Video Keywords** based on the **subject**", "Auto Detect": "Auto Detect", "Auto Generate": "Auto Generate", "Video Script": "Video Script (:blue[①Optional, use AI to generate ②Proper punctuation helps in generating subtitles])", "Save Script": "<PERSON>", "Crop Video": "Crop Video", "Video File": "Video File (:blue[1️⃣Supports uploading video files (limit 2G) 2️⃣For large files, it is recommended to directly import them into the ./resource/videos directory])", "Plot Description": "Plot Description (:blue[Can be obtained from https://www.tvmao.com/])", "Generate Video Keywords": "Click to use AI to generate **Video Keywords** based on the **script**", "Please Enter the Video Subject": "Please enter the video script first", "Generating Video Script and Keywords": "AI is generating the video script and keywords...", "Generating Video Keywords": "AI is generating the video keywords...", "Video Keywords": "Video Keywords (:blue[Long videos work better in conjunction with plot descriptions.])", "Video Settings": "**Video Settings**", "Video Concat Mode": "Video Concatenation Mode", "Random": "Random Concatenation (Recommended)", "Sequential": "Sequential Concatenation", "Video Ratio": "Video Ratio", "Portrait": "Portrait 9:16 (TikTok Video)", "Landscape": "Landscape 16:9 (Xigua Video)", "Clip Duration": "Maximum Clip Duration (Seconds) (**Not the total length of the video**, refers to the length of each **composite segment**)", "Number of Videos Generated Simultaneously": "Number of Videos Generated Simultaneously", "Audio Settings": "**Audio Settings**", "Speech Synthesis": "Speech Synthesis Voice (:red[**Keep consistent with the script language**. Note: V2 version performs better, but requires an API KEY])", "Speech Region": "Service Region (:red[Required, [Click to Get](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Key": "API Key (:red[Required, either Key 1 or Key 2 is acceptable [Click to Get](https://portal.azure.com/#view/Microsoft_Azure_ProjectOxford/CognitiveServicesHub/~/SpeechServices)])", "Speech Volume": "Speech Volume (1.0 represents 100%)", "Speech Rate": "Speech Rate (1.0 represents 1x speed)", "Male": "Male", "Female": "Female", "Background Music": "Background Music", "No Background Music": "No Background Music", "Random Background Music": "Random Background Music", "Custom Background Music": "Custom Background Music", "Custom Background Music File": "Please enter the file path of the custom background music", "Background Music Volume": "Background Music Volume (0.2 represents 20%, background sound should not be too loud)", "Subtitle Settings": "**Subtitle Settings**", "Enable Subtitles": "Enable Subtitles (If unchecked, the following settings will not take effect)", "Font": "Subtitle Font", "Position": "Subtitle Position", "Top": "Top", "Center": "Center", "Bottom": "Bottom (Recommended)", "Custom": "Custom Position (70, represents 70% from the top)", "Font Size": "Subtitle Size", "Font Color": "Subtitle Color", "Stroke Color": "Stroke Color", "Stroke Width": "Stroke Width", "Generate Video": "Generate Video", "Video Script and Subject Cannot Both Be Empty": "Video Subject and Video Script cannot both be empty", "Generating Video": "Generating video, please wait...", "Start Generating Video": "Start Generating Video", "Video Generation Completed": "Video Generation Completed", "Video Generation Failed": "Video Generation Failed", "You can download the generated video from the following links": "You can download the generated video from the following links", "Basic Settings": "**Basic Settings** (:blue[Click to expand])", "Language": "Interface Language", "Pexels API Key": "Pexels API Key ([Click to Get](https://www.pexels.com/api/)) :red[Recommended]", "Pixabay API Key": "Pixabay API Key ([Click to Get](https://pixabay.com/api/docs/#api_search_videos)) :red[Optional, if P<PERSON><PERSON> is unavailable, then choose Pixabay]", "LLM Provider": "LLM Provider", "API Key": "API Key (:red[Required, must be applied from the LLM provider's backend])", "Base Url": "Base Url (Optional)", "Account ID": "Account ID (Obtained from the URL of the Cloudflare dashboard)", "Model Name": "Model Name (:blue[Confirm the authorized model name from the LLM provider's backend])", "Please Enter the LLM API Key": "Please enter the **LLM API Key**", "Please Enter the Pexels API Key": "Please enter the **Pexels API Key**", "Please Enter the Pixabay API Key": "Please enter the **Pixabay API Key**", "Get Help": "One-stop AI video commentary + automated editing tool🎉🎉🎉\n\nFor any questions or suggestions, you can join the **community channel** for help or discussion: https://github.com/linyqh/NarratoAI/wiki", "Video Source": "Video Source", "TikTok": "TikTok (Support is coming soon)", "Bilibili": "Bilibili (Support is coming soon)", "Xiaohongshu": "<PERSON><PERSON><PERSON> (Support is coming soon)", "Local file": "Local file", "Play Voice": "Play Synthesized Voice", "Voice Example": "This is a sample text for testing voice synthesis", "Synthesizing Voice": "Synthesizing voice, please wait...", "TTS Provider": "TTS Provider", "Hide Log": "<PERSON>de Log", "Upload Local Files": "Upload Local Files", "File Uploaded Successfully": "File Uploaded Successfully", "Frame Interval (seconds)": "Frame Interval (seconds) (More keyframes consume more tokens)"}}