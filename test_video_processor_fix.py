#!/usr/bin/env python3
"""
测试 VideoProcessor 修复的脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.video_processor import VideoProcessor

def test_safe_conversions():
    """测试安全转换方法"""
    # 创建一个临时的 VideoProcessor 实例来测试方法
    class TestProcessor:
        def _safe_float_convert(self, value: str, default: float) -> float:
            try:
                if value in ('N/A', '', None):
                    return default
                return float(value)
            except (ValueError, TypeError):
                print(f"无法将 '{value}' 转换为浮点数，使用默认值 {default}")
                return default

        def _safe_int_convert(self, value: str, default: int) -> int:
            try:
                if value in ('N/A', '', None):
                    return default
                return int(float(value))
            except (ValueError, TypeError):
                print(f"无法将 '{value}' 转换为整数，使用默认值 {default}")
                return default

    processor = TestProcessor()
    
    # 测试各种情况
    test_cases = [
        ('N/A', 0.0, 'N/A 字符串'),
        ('', 0.0, '空字符串'),
        (None, 0.0, 'None 值'),
        ('123.45', 123.45, '正常浮点数'),
        ('invalid', 0.0, '无效字符串'),
        ('0', 0.0, '零值'),
    ]
    
    print("测试 _safe_float_convert 方法:")
    for value, expected, description in test_cases:
        result = processor._safe_float_convert(value, 0.0)
        status = "✓" if result == expected else "✗"
        print(f"  {status} {description}: '{value}' -> {result}")
    
    print("\n测试 _safe_int_convert 方法:")
    for value, expected, description in test_cases:
        expected_int = int(expected)
        result = processor._safe_int_convert(value, 0)
        status = "✓" if result == expected_int else "✗"
        print(f"  {status} {description}: '{value}' -> {result}")

if __name__ == "__main__":
    print("开始测试 VideoProcessor 修复...")
    test_safe_conversions()
    print("\n测试完成！")
