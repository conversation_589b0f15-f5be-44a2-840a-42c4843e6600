#!/usr/bin/env python
# -*- coding: UTF-8 -*-

'''
@Project: NarratoAI
@File   : prompt
<AUTHOR> 小林同学
@Date   : 2025/5/9 上午12:57 
'''
# 字幕剧情分析提示词
subtitle_plot_analysis_v1 = """
# 角色
你是一位专业的剧本分析师和剧情概括助手。

# 任务
我将为你提供一部短剧的完整字幕文本。请你基于这些字幕，完成以下任务：
1.  **整体剧情分析**：简要概括整个短剧的核心剧情脉络、主要冲突和结局（如果有的话）。
2.  **分段剧情解析与时间戳定位**：
    *   将整个短剧划分为若干个关键的剧情段落（例如：开端、发展、转折、高潮、结局，或根据具体情节自然划分）。
    *   段落数应该与字幕长度成正比。
    *   对于每一个剧情段落：
        *   **概括该段落的主要内容**：用简洁的语言描述这段剧情发生了什么。
        *   **标注对应的时间戳范围**：明确指出该剧情段落对应的开始字幕时间戳和结束字幕时间戳。请直接从字幕中提取时间信息。

# 输入格式
字幕内容通常包含时间戳和对话，例如：
```
00:00:05,000 --> 00:00:10,000
[角色A]: 你好吗？
00:00:10,500 --> 00:00:15,000
[角色B]: 我很好，谢谢。发生了一些有趣的事情。
... (更多字幕内容) ...
```
我将把实际字幕粘贴在下方。

# 输出格式要求
请按照以下格式清晰地呈现分析结果：

**一、整体剧情概括：**
[此处填写对整个短剧剧情的概括]

**二、分段剧情解析：**

**剧情段落 1：[段落主题/概括，例如：主角登场与背景介绍]**
*   **时间戳：** [开始时间戳] --> [结束时间戳]
*   **内容概要：** [对这段剧情的详细描述]

**剧情段落 2：[段落主题/概括，例如：第一个冲突出现]**
*   **时间戳：** [开始时间戳] --> [结束时间戳]
*   **内容概要：** [对这段剧情的详细描述]

... (根据实际剧情段落数量继续) ...

**剧情段落 N：[段落主题/概括，例如：结局与反思]**
*   **时间戳：** [开始时间戳] --> [结束时间戳]
*   **内容概要：** [对这段剧情的详细描述]

# 注意事项
*   请确保时间戳的准确性，直接引用字幕中的时间。
*   剧情段落的划分应合乎逻辑，能够反映剧情的起承转合。
*   语言表达应简洁、准确、客观。

# 限制
1. 严禁输出与分析结果无关的内容
2. 

# 请处理以下字幕：
"""

plot_writing = """
我是一个影视解说up主，需要为我的粉丝讲解短剧《%s》的剧情，目前正在解说剧情，希望能让粉丝通过我的解说了解剧情，并且产生 继续观看的兴趣，请生成一篇解说脚本，包含解说文案，以及穿插原声的片段，下面<plot>中的内容是短剧的剧情概述：

<plot>
%s
</plot>

请使用 json 格式进行输出；使用 <output> 中的输出格式：
<output>
{
  "items": [
    {
        "_id": 1, # 唯一递增id
        "timestamp": "00:00:05,390-00:00:10,430",
        "picture": "剧情描述或者备注",
        "narration": "解说文案，如果片段为穿插的原片片段，可以直接使用 ‘播放原片+_id‘ 进行占位",
        "OST": "值为 0 表示当前片段为解说片段，值为 1 表示当前片段为穿插的原片"
    }
}
</output>

<restriction>
1. 只输出 json 内容，不要输出其他任何说明性的文字
2. 解说文案的语言使用 简体中文
3. 严禁虚构剧情，所有画面只能从 <polt> 中摘取
4. 严禁虚构时间戳，所有时间戳范围只能从 <polt> 中摘取
</restriction>
"""