<div align="center">
<h1 align="center" style="font-size: 2cm;"> NarratoAI 😎📽️ </h1>
<h3 align="center">An all-in-one AI-powered tool for film commentary and automated video editing.🎬🎞️ </h3>


<h3>📖 English | <a href="README.md">简体中文</a> | <a href="README-ja.md">日本語</a> </h3>
<div align="center">

[//]: # (  <a href="https://trendshift.io/repositories/8731" target="_blank"><img src="https://trendshift.io/api/badge/repositories/8731" alt="harry0703%2FNarratoAI | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>)
</div>
<br>
NarratoAI is an automated video narration tool that provides an all-in-one solution for script writing, automated video editing, voice-over, and subtitle generation, powered by LLM to enhance efficient content creation.
<br>

[![madewithlove](https://img.shields.io/badge/made_with-%E2%9D%A4-red?style=for-the-badge&labelColor=orange)](https://github.com/linyqh/NarratoAI)
[![GitHub license](https://img.shields.io/github/license/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/blob/main/LICENSE)
[![GitHub issues](https://img.shields.io/github/issues/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/issues)
[![GitHub stars](https://img.shields.io/github/stars/linyqh/NarratoAI?style=for-the-badge)](https://github.com/linyqh/NarratoAI/stargazers)

<a href="https://discord.com/invite/V2pbAqqQNb" target="_blank">💬 Join the open source community to get project updates and the latest news.</a>

<h2><a href="https://p9mf6rjv3c.feishu.cn/wiki/SP8swLLZki5WRWkhuFvc2CyInDg?from=from_copylink" target="_blank">🎉🎉🎉 Official Documentation 🎉🎉🎉</a> </h2>
<h3>Home</h3>

![](docs/index-en.png)

<h3>Video Review Interface</h3>

![](docs/check-en.png)

</div>

## Latest News
- 2025.05.11 Released new version 0.6.0, supports **short drama commentary** and optimized editing process
- 2025.03.06 Released new version 0.5.2, supports DeepSeek R1 and DeepSeek V3 models for short drama mixing
- 2024.12.16 Released new version 0.3.9, supports Alibaba Qwen2-VL model for video understanding; supports short drama mixing
- 2024.11.24 Opened Discord community: https://discord.com/invite/V2pbAqqQNb
- 2024.11.11 Migrated open source community, welcome to join! [Join the official community](https://github.com/linyqh/NarratoAI/wiki)
- 2024.11.10 Released official documentation, details refer to [Official Documentation](https://p9mf6rjv3c.feishu.cn/wiki/SP8swLLZki5WRWkhuFvc2CyInDg)
- 2024.11.10 Released new version v0.3.5; optimized video editing process,

## Major Benefits 🎉
From now on, fully support DeepSeek model! Register to enjoy 20 million free tokens (worth 14 yuan platform quota), editing a 10-minute video only costs 0.1 yuan!  

🔥 Quick benefits:  
1️⃣ Click the link to register: https://cloud.siliconflow.cn/i/pyOKqFCV  
2️⃣ Log in with your phone number, **be sure to fill in the invitation code: pyOKqFCV**  
3️⃣ Receive a 14 yuan quota, experience high cost-effective AI editing quickly!  

💡 Low cost, high creativity:  
Silicon Flow API Key can be integrated with one click, doubling intelligent editing efficiency!  
(Note: The invitation code is the only proof for benefit collection, automatically credited after registration)  

Immediately take action to unlock your AI productivity with "pyOKqFCV"!

😊 Update Steps:
Integration Package: Click update.bat one-click update script
Code Build: Use git pull to fetch the latest code

## Announcement 📢
_**Note⚠️: Recently, someone has been impersonating the author on x (Twitter) to issue tokens on the pump.fun platform! This is a scam!!! Do not be deceived! Currently, NarratoAI has not made any official promotions on x (Twitter), please be cautious**_

Below is a screenshot of this person's x (Twitter) homepage

<img src="https://github.com/user-attachments/assets/c492ab99-52cd-4ba2-8695-1bd2073ecf12" alt="Screenshot_20250109_114131_Samsung Internet" style="width:30%; height:auto;">

## Future Plans 🥳
- [x] Windows Integration Pack Release
- [x] Optimized the story generation process and improved the generation effect
- [x] Released version 0.3.5 integration package
- [x] Support Alibaba Qwen2-VL large model for video understanding
- [x] Support short drama commentary
  - [x] One-click merge materials
  - [x] One-click transcription
  - [x] One-click clear cache
- [ ] Support exporting to Jianying drafts
- [X] Support short drama commentary
- [ ] Character face matching
- [ ] Support automatic matching based on voiceover, script, and video materials
- [ ] Support more TTS engines
- [ ] ...

## System Requirements 📦

- Recommended minimum: CPU with 4 cores or more, 8GB RAM or more, GPU is not required
- Windows 10/11 or MacOS 11.0 or above
- [Python 3.12+](https://www.python.org/downloads/)

## Feedback & Suggestions 📢

👏 1. You can submit [issue](https://github.com/linyqh/NarratoAI/issues) or [pull request](https://github.com/linyqh/NarratoAI/pulls)

💬 2. [Join the open source community exchange group](https://github.com/linyqh/NarratoAI/wiki)

📷 3. Follow the official account [NarratoAI助手] to grasp the latest news

## Reference Projects 📚
- https://github.com/FujiwaraChoki/MoneyPrinter
- https://github.com/harry0703/MoneyPrinterTurbo

This project was refactored based on the above projects with the addition of video narration features. Thanks to the original authors for their open-source spirit 🥳🥳🥳 

## Buy the Author a Cup of Coffee ☕️
<div style="display: flex; justify-content: space-between;">
  <img src="https://github.com/user-attachments/assets/5038ccfb-addf-4db1-9966-99415989fd0c" alt="Image 1" style="width: 350px; height: 350px; margin: auto;"/>
  <img src="https://github.com/user-attachments/assets/07d4fd58-02f0-425c-8b59-2ab94b4f09f8" alt="Image 2" style="width: 350px; height: 350px; margin: auto;"/>
</div>

## License 📝

Click to view [`LICENSE`](LICENSE) file

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=linyqh/NarratoAI&type=Date)](https://star-history.com/#linyqh/NarratoAI&Date)
