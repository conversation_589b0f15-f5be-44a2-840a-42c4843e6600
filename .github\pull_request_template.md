## PR 类型
请选择一个适当的标签（必选其一）：

- [ ] 破坏性变更 (breaking)
- [ ] 安全修复 (security)
- [ ] 新功能 (feature)
- [ ] Bug修复 (bug)
- [ ] 代码重构 (refactor)
- [ ] 依赖升级 (upgrade)
- [ ] 文档更新 (docs)
- [ ] 翻译相关 (lang-all)
- [ ] 内部改进 (internal)

## 描述
<!-- 请提供对此次更改的清晰描述。为什么需要这个更改？它解决了什么问题？ -->

## 相关 Issue
<!-- 请链接相关的 issue（如果有）。例如：Fixes #123 -->

## 更改内容
<!-- 详细描述具体更改了什么 -->

- xxx
- xxx
- xxx

## 测试
<!-- 描述如何测试你的更改 -->

- [ ] 单元测试
- [ ] 集成测试
- [ ] 手动测试

## 截图（如果适用）
<!-- 如果是UI相关的更改，请提供截图 -->

## 检查清单

- [ ] 我的代码遵循项目的代码风格
- [ ] 我已经添加了必要的测试
- [ ] 我已经更新了相关文档
- [ ] 我的更改不会引入新的警告
- [ ] PR 标题清晰描述了更改内容

## 补充说明
<!-- 任何其他相关信息 -->